/**
 * @fileoverview Embedding generation manager with caching and error handling.
 */

import { ModelFactory } from '../factories/ModelFactory.js';
import { GeminiAIError, ApiKeyError } from '../errors/GeminiErrors.js';

/**
 * Manages embedding generation with caching and error handling
 */
export class EmbeddingManager {
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
		this.embeddingsInstance = null;
		this.currentApiKey = null;
	}

	/**
	 * Creates or reuses an embeddings instance
	 * @param {string} apiKey - Valid Gemini API key
	 * @returns {GoogleGenerativeAIEmbeddings} Embeddings instance
	 */
	getEmbeddingsInstance(apiKey) {
		if (!this.embeddingsInstance || this.currentApiKey !== apiKey) {
			this.embeddingsInstance = ModelFactory.createEmbeddingsInstance(apiKey);
			this.currentApiKey = apiKey;
		}
		return this.embeddingsInstance;
	}

	/**
	 * Generates an embedding for the given text with enhanced error handling and API key rotation
	 * @param {object} env - Environment variables
	 * @param {string} textToEmbed - Text to generate embedding for
	 * @returns {Promise<number[]>} Embedding vector
	 * @throws {GeminiAIError} If API key is missing or text is invalid
	 */
	async generateEmbedding(env, textToEmbed) {
		// Input validation
		if (!textToEmbed || typeof textToEmbed !== 'string' || textToEmbed.length === 0) {
			throw new GeminiAIError('Invalid text input for embedding: must be a non-empty string');
		}

		if (!env) {
			throw new GeminiAIError('Environment variables are required');
		}

		// Load available API keys
		const apiKeys = await this.apiKeyManager.loadGeminiApiKeys(env);
		if (apiKeys.length === 0) {
			throw new ApiKeyError('No Gemini API keys configured in environment variables');
		}

		let lastError = null;
		let attempts = 0;
		const maxAttempts = Math.min(apiKeys.length, 3); // Try up to 3 keys or all available keys

		// Try multiple API keys with rotation
		while (attempts < maxAttempts) {
			try {
				const apiKey = await this.apiKeyManager.getNextGeminiApiKey(env);
				if (!apiKey) {
					throw new ApiKeyError('No Generative AI API key available for embedding');
				}

				// Create or reuse embeddings instance
				const embeddings = this.getEmbeddingsInstance(apiKey);

				// Generate embedding with enhanced context
				const result = await embeddings.embedQuery(textToEmbed);

				// Add basic validation for result
				if (!result || !Array.isArray(result) || result.length === 0) {
					throw new GeminiAIError('Empty or invalid embedding response from API');
				}

				// Success - return the result
				return result;
			} catch (error) {
				attempts++;
				lastError = error;

				// Enhanced error logging with context
				const errorContext = {
					textLength: textToEmbed?.length || 0,
					error: error.message,
					timestamp: new Date().toISOString(),
					attempt: attempts,
					maxAttempts: maxAttempts,
				};

				console.error('Embedding generation failed:', errorContext);

				// If this is a GeminiAIError (validation error), don't retry
				if (error instanceof GeminiAIError) {
					throw error;
				}

				// Update API key status for API-related errors
				if (this.currentApiKey) {
					await this.apiKeyManager.updateKeyStatus(this.currentApiKey, error);
				}

				// If we've exhausted all attempts, break the loop
				if (attempts >= maxAttempts) {
					break;
				}

				// Log retry attempt
				console.log(`Retrying embedding generation with next API key (attempt ${attempts + 1}/${maxAttempts})`);
			}
		}

		// All attempts failed
		throw new GeminiAIError(`Failed to generate embedding after ${attempts} attempts: ${lastError?.message || 'Unknown error'}`, lastError);
	}
}
