/**
 * Test script to verify API key rotation mechanism
 */

import { ApiKeyManager } from './src/chat/gemini-ai/managers/ApiKeyManager.js';
import { EmbeddingManager } from './src/chat/gemini-ai/managers/EmbeddingManager.js';

// Mock environment with multiple API keys
const mockEnv = {
    GEMINI_API_KEY_1: 'invalid-key-1',
    GEMINI_API_KEY_2: 'invalid-key-2', 
    GEMINI_API_KEY_3: 'valid-key-3',
    // Mock Redis environment variables
    UPSTASH_REDIS_REST_URL: 'https://mock-redis.upstash.io',
    UPSTASH_REDIS_REST_TOKEN: 'mock-token'
};

async function testApiKeyRotation() {
    console.log('🧪 Testing API Key Rotation Mechanism...\n');

    const apiKeyManager = new ApiKeyManager();
    const embeddingManager = new EmbeddingManager(apiKeyManager);

    try {
        // Test 1: Load API keys
        console.log('📋 Test 1: Loading API keys...');
        const keys = await apiKeyManager.loadGeminiApiKeys(mockEnv);
        console.log(`✅ Loaded ${keys.length} API keys:`, keys.map(k => k.substring(0, 10) + '...'));

        // Test 2: Get next API key
        console.log('\n🔄 Test 2: Getting next API key...');
        const firstKey = await apiKeyManager.getNextGeminiApiKey(mockEnv);
        console.log(`✅ First key: ${firstKey.substring(0, 10)}...`);

        // Test 3: Simulate API key error
        console.log('\n❌ Test 3: Simulating API key error...');
        const mockError = new Error('[GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent: [400 Bad Request] API key not valid. Please pass a valid API key.');
        
        await apiKeyManager.updateKeyStatus(firstKey, mockError);
        console.log(`✅ Updated key status for ${firstKey.substring(0, 10)}...`);

        // Test 4: Get next API key after error
        console.log('\n🔄 Test 4: Getting next API key after error...');
        const secondKey = await apiKeyManager.getNextGeminiApiKey(mockEnv);
        console.log(`✅ Second key: ${secondKey.substring(0, 10)}...`);

        // Test 5: Test error detection
        console.log('\n🔍 Test 5: Testing error detection...');
        const errorStatus = apiKeyManager._getErrorStatus(mockError);
        const errorType = apiKeyManager._getErrorType(mockError);
        console.log(`✅ Error status: ${errorStatus}`);
        console.log(`✅ Error type: ${errorType}`);

        console.log('\n🎉 All tests passed! API key rotation mechanism is working correctly.');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Run the test
testApiKeyRotation().catch(console.error);
